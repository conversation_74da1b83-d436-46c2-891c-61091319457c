#!/usr/bin/env python3
"""
测试训练脚本 - 验证 train_3D_resnet_cv.py 是否能正常工作
使用少量数据和少量epoch进行快速测试
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from sklearn.model_selection import KFold
from monai.transforms import Compose, ScaleIntensity, Resize, ToTensor
from train_3D_resnet_cv import NiftiDataset, add_channel_dim
from resnet3D_model import resnet3d

def quick_test():
    """快速测试训练流程"""
    print("=== 快速训练测试 ===")
    
    # 检查设备
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 数据设置
    data_dirs = {'HC': './HC', 'MCI': './MCI'}
    classes = ['HC', 'MCI']
    
    # 数据变换
    transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(32, 64, 64)),  # 使用更小的尺寸加速测试
        ToTensor()
    ])
    
    # 创建数据集
    dataset = NiftiDataset(data_dirs, classes, transform=transform)
    print(f"数据集大小: {len(dataset)}")
    
    # 使用少量数据进行测试
    test_indices = list(range(min(20, len(dataset))))  # 最多20个样本
    test_dataset = Subset(dataset, test_indices)
    
    # 简单的训练/验证分割
    train_size = int(0.8 * len(test_dataset))
    val_size = len(test_dataset) - train_size
    
    train_subset = Subset(test_dataset, list(range(train_size)))
    val_subset = Subset(test_dataset, list(range(train_size, len(test_dataset))))
    
    print(f"训练样本: {len(train_subset)}, 验证样本: {len(val_subset)}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_subset, batch_size=2, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_subset, batch_size=2, shuffle=False, num_workers=0)
    
    # 创建模型
    model = resnet3d(model_name='resnet18', in_channels=1, num_classes=2, init_weights=True)
    model.to(device)
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    print("\n开始训练测试...")
    
    # 训练几个epoch
    epochs = 2
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()
            
            print(f"Epoch {epoch+1}, Batch {batch_idx+1}: Loss = {loss.item():.4f}")
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                loss = criterion(output, target)
                
                val_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                val_total += target.size(0)
                val_correct += (predicted == target).sum().item()
        
        train_acc = 100. * train_correct / train_total if train_total > 0 else 0
        val_acc = 100. * val_correct / val_total if val_total > 0 else 0
        
        print(f"Epoch {epoch+1}/{epochs}:")
        print(f"  训练 - Loss: {train_loss/len(train_loader):.4f}, Acc: {train_acc:.2f}%")
        print(f"  验证 - Loss: {val_loss/len(val_loader):.4f}, Acc: {val_acc:.2f}%")
    
    print("\n✅ 训练测试完成！代码运行正常。")
    return True

if __name__ == "__main__":
    try:
        quick_test()
        print("\n🎉 train_3D_resnet_cv.py 已经可以正常使用当前的 HC 和 MCI 数据进行训练！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
